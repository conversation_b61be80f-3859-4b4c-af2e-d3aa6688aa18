<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{12345678-ABCD-EF12-3456-************}</ProjectGuid>
    <RootNamespace>NexusProtection</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>NexusProtection</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;NEXUS_PROTECTION_EXPORTS;WIN32_LEAN_AND_MEAN;NOMINMAX;UNICODE;_UNICODE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)world\Headers;$(ProjectDir)authentication\Headers;$(ProjectDir)combat\Headers;$(ProjectDir)database\Headers;$(ProjectDir)economy\Headers;$(ProjectDir)items\Headers;$(ProjectDir)network\Headers;$(ProjectDir)player\Headers;$(ProjectDir)security\Headers;$(ProjectDir)system\Headers;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <TreatWarningAsError>false</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <AdditionalDependencies>kernel32.lib;user32.lib;advapi32.lib;ws2_32.lib;winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;NEXUS_PROTECTION_EXPORTS;WIN32_LEAN_AND_MEAN;NOMINMAX;UNICODE;_UNICODE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)world\Headers;$(ProjectDir)authentication\Headers;$(ProjectDir)combat\Headers;$(ProjectDir)database\Headers;$(ProjectDir)economy\Headers;$(ProjectDir)items\Headers;$(ProjectDir)network\Headers;$(ProjectDir)player\Headers;$(ProjectDir)security\Headers;$(ProjectDir)system\Headers;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <TreatWarningAsError>false</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <AdditionalDependencies>kernel32.lib;user32.lib;advapi32.lib;ws2_32.lib;winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;NEXUS_PROTECTION_EXPORTS;WIN32_LEAN_AND_MEAN;NOMINMAX;UNICODE;_UNICODE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)world\Headers;$(ProjectDir)authentication\Headers;$(ProjectDir)combat\Headers;$(ProjectDir)database\Headers;$(ProjectDir)economy\Headers;$(ProjectDir)items\Headers;$(ProjectDir)network\Headers;$(ProjectDir)player\Headers;$(ProjectDir)security\Headers;$(ProjectDir)system\Headers;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <TreatWarningAsError>false</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <AdditionalDependencies>kernel32.lib;user32.lib;advapi32.lib;ws2_32.lib;winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;NEXUS_PROTECTION_EXPORTS;WIN32_LEAN_AND_MEAN;NOMINMAX;UNICODE;_UNICODE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)world\Headers;$(ProjectDir)authentication\Headers;$(ProjectDir)combat\Headers;$(ProjectDir)database\Headers;$(ProjectDir)economy\Headers;$(ProjectDir)items\Headers;$(ProjectDir)network\Headers;$(ProjectDir)player\Headers;$(ProjectDir)security\Headers;$(ProjectDir)system\Headers;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <TreatWarningAsError>false</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <AdditionalDependencies>kernel32.lib;user32.lib;advapi32.lib;ws2_32.lib;winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <!-- Explicitly include world module files for proper Solution Explorer organization -->
  <ItemGroup Label="World Module Files">
    <!-- World Headers -->
    <ClInclude Include="world\Headers\MonsterEventRespawn.h" />
    <ClInclude Include="world\Headers\WorldAvatarEntry.h" />
    <ClInclude Include="world\Headers\WorldAvatarExit.h" />
    <ClInclude Include="world\Headers\WorldServiceInform.h" />
    <ClInclude Include="world\Headers\EnterWorldResult.h" />
    <ClInclude Include="world\Headers\AlterWorldService.h" />
    <ClInclude Include="world\Headers\OpenWorldSuccessResult.h" />
    <ClInclude Include="world\Headers\OpenWorldFailureResult.h" />
    <ClInclude Include="world\Headers\EnterWorldRequest.h" />
    <ClInclude Include="world\Headers\ExitWorldRequest.h" />
    <ClInclude Include="world\Headers\CreateCMonster.h" />
    <ClInclude Include="world\Headers\CMonsterAI.h" />
    <ClInclude Include="world\Headers\CMapData.h" />
    <ClInclude Include="world\Headers\CMapOperation.h" />
    <ClInclude Include="world\Headers\CMapDisplay.h" />
    <ClInclude Include="world\Headers\CMapExtend.h" />
    <ClInclude Include="world\Headers\CMapTab.h" />
    <ClInclude Include="world\Headers\BossSchedule_Map.h" />
    <ClInclude Include="world\Headers\CCircleZone.h" />
    <ClInclude Include="world\Headers\CDarkHoleChannel.h" />
    <ClInclude Include="world\Headers\CGravityStoneRegener.h" />
    <ClInclude Include="world\Headers\CMonster.h" />
    <ClInclude Include="world\Headers\CMonsterAggroMgr.h" />
    <ClInclude Include="world\Headers\CMonsterHierarchy.h" />
    <ClInclude Include="world\Headers\MonsterSetInfoData.h" />
    <ClInclude Include="world\Headers\MonsterStateData.h" />
    <ClInclude Include="world\Headers\MonsterUtils.h" />
    <ClInclude Include="world\Headers\NPCQuestIndexTempData.h" />
    <ClInclude Include="world\Headers\CWorldSchedule.h" />
    <ClInclude Include="world\Headers\CRFMonsterAIMgr.h" />
    <ClInclude Include="world\Headers\CMonsterEventRespawn.h" />
    <ClInclude Include="world\Headers\CreateSetData.h" />
    <ClInclude Include="world\Headers\MonsterSFContDamageTolerance.h" />
    <ClInclude Include="world\Headers\MonsterRecovery.h" />
    <ClInclude Include="world\Headers\MonsterAICreator.h" />
    <ClInclude Include="world\Headers\CMonsterEventSet.h" />
    <ClInclude Include="world\Headers\CMoveMapLimitManager.h" />
    <ClInclude Include="world\Headers\CMoveMapLimitInfoList.h" />
    <ClInclude Include="world\Headers\CMoveMapLimitInfo.h" />
    <ClInclude Include="world\Headers\CMoveMapLimitInfoPortal.h" />
    <ClInclude Include="world\Headers\CMoveMapLimitRightInfoList.h" />
    <ClInclude Include="world\Headers\CMonsterHelper.h" />
    <ClInclude Include="world\Headers\CMerchant.h" />
    <ClInclude Include="world\Headers\MapStructures.h" />
    <ClInclude Include="world\Headers\QuestStructures.h" />
    <ClInclude Include="world\Headers\CQuestMgr.h" />
    <ClInclude Include="world\Headers\DfAIMgr.h" />
    <ClInclude Include="world\Headers\CBsp.h" />
    <ClInclude Include="player\Headers\CMonsterAttack.h" />
  </ItemGroup>
  <!-- Automatically include all header files from other modules (world module uses explicit entries) -->
  <ItemGroup Label="Header Files">
    <ClInclude Include="authentication\Headers\**\*.h" />
    <ClInclude Include="authentication\Headers\**\*.hpp" />
    <ClInclude Include="combat\Headers\**\*.h" />
    <ClInclude Include="combat\Headers\**\*.hpp" />
    <ClInclude Include="database\Headers\**\*.h" />
    <ClInclude Include="database\Headers\**\*.hpp" />
    <ClInclude Include="economy\Headers\**\*.h" />
    <ClInclude Include="economy\Headers\**\*.hpp" />
    <ClInclude Include="items\Headers\**\*.h" />
    <ClInclude Include="items\Headers\**\*.hpp" />
    <ClInclude Include="network\Headers\**\*.h" />
    <ClInclude Include="network\Headers\**\*.hpp" />
    <ClInclude Include="player\Headers\**\*.h" />
    <ClInclude Include="player\Headers\**\*.hpp" />
    <ClInclude Include="security\Headers\**\*.h" />
    <ClInclude Include="security\Headers\**\*.hpp" />
    <ClInclude Include="system\Headers\**\*.h" />
    <ClInclude Include="system\Headers\**\*.hpp" />
  </ItemGroup>
  <!-- Explicitly include world module source files for proper Solution Explorer organization -->
  <ItemGroup Label="World Module Source Files">
    <!-- World Sources -->
    <ClCompile Include="world\Source\MonsterEventRespawn.cpp" />
    <ClCompile Include="world\Source\WorldAvatarEntry.cpp" />
    <ClCompile Include="world\Source\WorldAvatarExit.cpp" />
    <ClCompile Include="world\Source\WorldServiceInform.cpp" />
    <ClCompile Include="world\Source\EnterWorldResult.cpp" />
    <ClCompile Include="world\Source\AlterWorldService.cpp" />
    <ClCompile Include="world\Source\OpenWorldSuccessResult.cpp" />
    <ClCompile Include="world\Source\OpenWorldFailureResult.cpp" />
    <ClCompile Include="world\Source\EnterWorldRequest.cpp" />
    <ClCompile Include="world\Source\ExitWorldRequest.cpp" />
    <ClCompile Include="world\Source\CreateCMonster.cpp" />
    <ClCompile Include="world\Source\CMonsterAI.cpp" />
    <ClCompile Include="world\Source\CMapData.cpp" />
    <ClCompile Include="world\Source\CMapOperation.cpp" />
    <ClCompile Include="world\Source\CMapDisplay.cpp" />
    <ClCompile Include="world\Source\CMapExtend.cpp" />
    <ClCompile Include="world\Source\CMapTab.cpp" />
    <ClCompile Include="world\Source\BossSchedule_Map.cpp" />
    <ClCompile Include="world\Source\CCircleZone.cpp" />
    <ClCompile Include="world\Source\CDarkHoleChannel.cpp" />
    <ClCompile Include="world\Source\CGravityStoneRegener.cpp" />
    <ClCompile Include="world\Source\CMonster.cpp" />
    <ClCompile Include="world\Source\CMonsterAggroMgr.cpp" />
    <ClCompile Include="world\Source\CMonsterHierarchy.cpp" />
    <ClCompile Include="world\Source\MonsterStateData.cpp" />
    <ClCompile Include="world\Source\MonsterSFContDamageTolerance.cpp" />
    <ClCompile Include="world\Source\MonsterRecovery.cpp" />
    <ClCompile Include="world\Source\MonsterAICreator.cpp" />
    <ClCompile Include="world\Source\CMonsterEventSet.cpp" />
    <ClCompile Include="world\Source\CMoveMapLimitManager.cpp" />
    <ClCompile Include="world\Source\CMoveMapLimitInfoList.cpp" />
    <ClCompile Include="world\Source\CMoveMapLimitInfo.cpp" />
    <ClCompile Include="world\Source\CMoveMapLimitInfoPortal.cpp" />
    <ClCompile Include="world\Source\CMoveMapLimitRightInfoList.cpp" />
    <ClCompile Include="world\Source\CMonsterHelper.cpp" />
    <ClCompile Include="world\Source\CMerchant.cpp" />
    <ClCompile Include="world\Source\MonsterSetInfoData.cpp" />
    <ClCompile Include="world\Source\MonsterUtils.cpp" />
    <ClCompile Include="world\Source\NPCQuestIndexTempData.cpp" />
    <ClCompile Include="world\Source\CWorldSchedule.cpp" />
    <ClCompile Include="world\Source\CRFMonsterAIMgr.cpp" />
    <ClCompile Include="world\Source\CMonsterEventRespawn.cpp" />
    <ClCompile Include="world\Source\CreateSetData.cpp" />
    <ClCompile Include="world\Source\CMapOperation_LoadMaps.cpp" />
    <ClCompile Include="world\Source\MapStructureUtils.cpp" />
    <ClCompile Include="world\Source\CQuestMgr_CheckNPCQuestList.cpp" />
    <ClCompile Include="world\Source\CQuestMgr_Core.cpp" />
    <ClCompile Include="world\Source\QuestUtils.cpp" />
    <ClCompile Include="world\Source\DfAIMgr_CheckGen.cpp" />
    <ClCompile Include="world\Source\DfAIMgr_Core.cpp" />
    <ClCompile Include="world\Source\CBsp_LoadEntities.cpp" />
    <ClCompile Include="world\Source\CBsp_Core.cpp" />
    <ClCompile Include="player\Source\CMonsterAttack_Core.cpp" />
    <ClCompile Include="player\Source\CMonsterAttack_Monster.cpp" />
    <ClCompile Include="player\Source\CMonsterAttack_Params.cpp" />
    <ClCompile Include="player\Source\CPlayerAttack_Core.cpp" />
    <ClCompile Include="player\Source\CPlayerAttack_Unit.cpp" />
    <ClCompile Include="player\Source\CPlayerAttack_Params.cpp" />
  </ItemGroup>
  <!-- World Module Documentation -->
  <!-- Automatically include all source files from other modules (world module uses explicit entries) -->
  <ItemGroup Label="Source Files">
    <ClCompile Include="authentication\Source\**\*.cpp" />
    <ClCompile Include="authentication\Source\**\*.c" />
    <ClCompile Include="combat\Source\**\*.cpp" />
    <ClCompile Include="combat\Source\**\*.c" />
    <ClCompile Include="database\Source\**\*.cpp" />
    <ClCompile Include="database\Source\**\*.c" />
    <ClCompile Include="economy\Source\**\*.cpp" />
    <ClCompile Include="economy\Source\**\*.c" />
    <ClCompile Include="items\Source\**\*.cpp" />
    <ClCompile Include="items\Source\**\*.c" />
    <ClCompile Include="network\Source\**\*.cpp" />
    <ClCompile Include="network\Source\**\*.c" />
    <ClCompile Include="player\Source\**\*.cpp" />
    <ClCompile Include="player\Source\**\*.c" />
    <ClCompile Include="security\Source\**\*.cpp" />
    <ClCompile Include="security\Source\**\*.c" />
    <ClCompile Include="system\Source\**\*.cpp" />
    <ClCompile Include="system\Source\**\*.c" />
  </ItemGroup>
  <!-- Include documentation and build files -->
  <ItemGroup Label="Documentation">
    <None Include="CMakeLists.txt" />
    <None Include="world\Documents\**\*.txt" />
    <None Include="authentication\Documents\**\*.md" />
    <None Include="authentication\Documents\**\*.txt" />
    <None Include="combat\Documents\**\*.md" />
    <None Include="combat\Documents\**\*.txt" />
    <None Include="database\Documents\**\*.md" />
    <None Include="database\Documents\**\*.txt" />
    <None Include="economy\Documents\**\*.md" />
    <None Include="economy\Documents\**\*.txt" />
    <None Include="items\Documents\**\*.md" />
    <None Include="items\Documents\**\*.txt" />
    <None Include="network\Documents\**\*.md" />
    <None Include="network\Documents\**\*.txt" />
    <None Include="player\Documents\**\*.md" />
    <None Include="player\Documents\**\*.txt" />
    <None Include="security\Documents\**\*.md" />
    <None Include="security\Documents\**\*.txt" />
    <None Include="system\Documents\**\*.md" />
    <None Include="system\Documents\**\*.txt" />
  </ItemGroup>
  <!-- Include test files if they exist -->
  <ItemGroup Label="Test Files">
    <ClCompile Include="world\Tests\**\*.cpp" Condition="Exists('world\Tests')" />
    <ClCompile Include="authentication\Tests\**\*.cpp" Condition="Exists('authentication\Tests')" />
    <ClCompile Include="combat\Tests\**\*.cpp" Condition="Exists('combat\Tests')" />
    <ClCompile Include="database\Tests\**\*.cpp" Condition="Exists('database\Tests')" />
    <ClCompile Include="economy\Tests\**\*.cpp" Condition="Exists('economy\Tests')" />
    <ClCompile Include="items\Tests\**\*.cpp" Condition="Exists('items\Tests')" />
    <ClCompile Include="network\Tests\**\*.cpp" Condition="Exists('network\Tests')" />
    <ClCompile Include="player\Tests\**\*.cpp" Condition="Exists('player\Tests')" />
    <ClCompile Include="security\Tests\**\*.cpp" Condition="Exists('security\Tests')" />
    <ClCompile Include="system\Tests\**\*.cpp" Condition="Exists('system\Tests')" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>